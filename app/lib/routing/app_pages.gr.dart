// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i25;
import 'package:flutter/material.dart' as _i26;
import 'package:juno_plus/app.dart' as _i22;
import 'package:juno_plus/pages/connectivity/pair_page.dart' as _i11;
import 'package:juno_plus/pages/connectivity/troubleshooting_page.dart' as _i23;
import 'package:juno_plus/pages/dashboard/therapy_analytics_chart_page.dart'
    as _i1;
import 'package:juno_plus/pages/dashboard/dashboard_page.dart' as _i2;
import 'package:juno_plus/pages/dashboard/extended_calendar.dart' as _i4;
import 'package:juno_plus/pages/dashboard/period_tracking_calendar.dart'
    as _i12;
import 'package:juno_plus/pages/help_center/help_center_home.dart' as _i6;
import 'package:juno_plus/pages/home_page.dart' as _i7;
import 'package:juno_plus/pages/login/email_verification_page.dart' as _i3;
import 'package:juno_plus/pages/login/login_page.dart' as _i8;
import 'package:juno_plus/pages/login/reset_password_page.dart' as _i19;
import 'package:juno_plus/pages/login/sign_up_page.dart' as _i21;
import 'package:juno_plus/pages/login/welcome_page.dart' as _i24;
import 'package:juno_plus/pages/medications/medication.dart' as _i9;
import 'package:juno_plus/pages/notifications/notifications_page.dart' as _i10;
import 'package:juno_plus/pages/onboarding/get_started_page.dart' as _i5;
import 'package:juno_plus/pages/remote/remote_experiment_Page.dart' as _i15;
import 'package:juno_plus/pages/remote/remote_one_page.dart' as _i16;
import 'package:juno_plus/pages/remote/remote_page.dart' as _i17;
import 'package:juno_plus/pages/remote/remote_two_page.dart' as _i18;
import 'package:juno_plus/pages/settings/profile_page.dart' as _i13;
import 'package:juno_plus/pages/settings/profile_picture_page.dart' as _i14;
import 'package:juno_plus/pages/settings/settings_page.dart' as _i20;

/// generated route for
/// [_i1.TherapyAnalyticsChartPage]
class TherapyAnalyticsChartRoute extends _i25.PageRouteInfo<void> {
  const TherapyAnalyticsChartRoute({List<_i25.PageRouteInfo>? children})
      : super(TherapyAnalyticsChartRoute.name, initialChildren: children);

  static const String name = 'TherapyAnalyticsChartRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i1.TherapyAnalyticsChartPage();
    },
  );
}

/// generated route for
/// [_i2.DashboardPage]
class DashboardRoute extends _i25.PageRouteInfo<void> {
  const DashboardRoute({List<_i25.PageRouteInfo>? children})
      : super(DashboardRoute.name, initialChildren: children);

  static const String name = 'DashboardRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i2.DashboardPage();
    },
  );
}

/// generated route for
/// [_i3.EmailVerificationPage]
class EmailVerificationRoute extends _i25.PageRouteInfo<void> {
  const EmailVerificationRoute({List<_i25.PageRouteInfo>? children})
      : super(EmailVerificationRoute.name, initialChildren: children);

  static const String name = 'EmailVerificationRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i3.EmailVerificationPage();
    },
  );
}

/// generated route for
/// [_i4.ExtendedCalenderPage]
class ExtendedCalenderRoute extends _i25.PageRouteInfo<void> {
  const ExtendedCalenderRoute({List<_i25.PageRouteInfo>? children})
      : super(ExtendedCalenderRoute.name, initialChildren: children);

  static const String name = 'ExtendedCalenderRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i4.ExtendedCalenderPage();
    },
  );
}

/// generated route for
/// [_i5.GetStartedPage]
class GetStartedRoute extends _i25.PageRouteInfo<void> {
  const GetStartedRoute({List<_i25.PageRouteInfo>? children})
      : super(GetStartedRoute.name, initialChildren: children);

  static const String name = 'GetStartedRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i5.GetStartedPage();
    },
  );
}

/// generated route for
/// [_i6.HelpCenterHomePage]
class HelpCenterHomeRoute extends _i25.PageRouteInfo<void> {
  const HelpCenterHomeRoute({List<_i25.PageRouteInfo>? children})
      : super(HelpCenterHomeRoute.name, initialChildren: children);

  static const String name = 'HelpCenterHomeRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i6.HelpCenterHomePage();
    },
  );
}

/// generated route for
/// [_i7.HomePage]
class HomeRoute extends _i25.PageRouteInfo<void> {
  const HomeRoute({List<_i25.PageRouteInfo>? children})
      : super(HomeRoute.name, initialChildren: children);

  static const String name = 'HomeRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i7.HomePage();
    },
  );
}

/// generated route for
/// [_i8.LoginPage]
class LoginRoute extends _i25.PageRouteInfo<void> {
  const LoginRoute({List<_i25.PageRouteInfo>? children})
      : super(LoginRoute.name, initialChildren: children);

  static const String name = 'LoginRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i8.LoginPage();
    },
  );
}

/// generated route for
/// [_i9.MedicationPage]
class MedicationRoute extends _i25.PageRouteInfo<void> {
  const MedicationRoute({List<_i25.PageRouteInfo>? children})
      : super(MedicationRoute.name, initialChildren: children);

  static const String name = 'MedicationRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i9.MedicationPage();
    },
  );
}

/// generated route for
/// [_i10.NotificationsPage]
class NotificationsRoute extends _i25.PageRouteInfo<void> {
  const NotificationsRoute({List<_i25.PageRouteInfo>? children})
      : super(NotificationsRoute.name, initialChildren: children);

  static const String name = 'NotificationsRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i10.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i11.PairDevicePage]
class PairDeviceRoute extends _i25.PageRouteInfo<void> {
  const PairDeviceRoute({List<_i25.PageRouteInfo>? children})
      : super(PairDeviceRoute.name, initialChildren: children);

  static const String name = 'PairDeviceRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i11.PairDevicePage();
    },
  );
}

/// generated route for
/// [_i12.PeriodTrackingCalendarPage]
class PeriodTrackingCalendarRoute extends _i25.PageRouteInfo<void> {
  const PeriodTrackingCalendarRoute({List<_i25.PageRouteInfo>? children})
      : super(PeriodTrackingCalendarRoute.name, initialChildren: children);

  static const String name = 'PeriodTrackingCalendarRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i12.PeriodTrackingCalendarPage();
    },
  );
}

/// generated route for
/// [_i13.ProfilePage]
class ProfileRoute extends _i25.PageRouteInfo<ProfileRouteArgs> {
  ProfileRoute({_i26.Key? key, List<_i25.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          args: ProfileRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ProfileRouteArgs>(
        orElse: () => const ProfileRouteArgs(),
      );
      return _i13.ProfilePage(key: args.key);
    },
  );
}

class ProfileRouteArgs {
  const ProfileRouteArgs({this.key});

  final _i26.Key? key;

  @override
  String toString() {
    return 'ProfileRouteArgs{key: $key}';
  }
}

/// generated route for
/// [_i14.ProfilePicturePage]
class ProfilePictureRoute extends _i25.PageRouteInfo<void> {
  const ProfilePictureRoute({List<_i25.PageRouteInfo>? children})
      : super(ProfilePictureRoute.name, initialChildren: children);

  static const String name = 'ProfilePictureRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i14.ProfilePicturePage();
    },
  );
}

/// generated route for
/// [_i15.RemoteExperimentPage]
class RemoteExperimentRoute extends _i25.PageRouteInfo<void> {
  const RemoteExperimentRoute({List<_i25.PageRouteInfo>? children})
      : super(RemoteExperimentRoute.name, initialChildren: children);

  static const String name = 'RemoteExperimentRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i15.RemoteExperimentPage();
    },
  );
}

/// generated route for
/// [_i16.RemoteOnePage]
class RemoteOneRoute extends _i25.PageRouteInfo<void> {
  const RemoteOneRoute({List<_i25.PageRouteInfo>? children})
      : super(RemoteOneRoute.name, initialChildren: children);

  static const String name = 'RemoteOneRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i16.RemoteOnePage();
    },
  );
}

/// generated route for
/// [_i17.RemotePage]
class RemoteRoute extends _i25.PageRouteInfo<void> {
  const RemoteRoute({List<_i25.PageRouteInfo>? children})
      : super(RemoteRoute.name, initialChildren: children);

  static const String name = 'RemoteRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i17.RemotePage();
    },
  );
}

/// generated route for
/// [_i18.RemoteTwoPage]
class RemoteTwoRoute extends _i25.PageRouteInfo<void> {
  const RemoteTwoRoute({List<_i25.PageRouteInfo>? children})
      : super(RemoteTwoRoute.name, initialChildren: children);

  static const String name = 'RemoteTwoRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i18.RemoteTwoPage();
    },
  );
}

/// generated route for
/// [_i19.ResetPasswordPage]
class ResetPasswordRoute extends _i25.PageRouteInfo<void> {
  const ResetPasswordRoute({List<_i25.PageRouteInfo>? children})
      : super(ResetPasswordRoute.name, initialChildren: children);

  static const String name = 'ResetPasswordRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i19.ResetPasswordPage();
    },
  );
}

/// generated route for
/// [_i20.SettingsPage]
class SettingsRoute extends _i25.PageRouteInfo<SettingsRouteArgs> {
  SettingsRoute({_i26.Key? key, List<_i25.PageRouteInfo>? children})
      : super(
          SettingsRoute.name,
          args: SettingsRouteArgs(key: key),
          initialChildren: children,
        );

  static const String name = 'SettingsRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<SettingsRouteArgs>(
        orElse: () => const SettingsRouteArgs(),
      );
      return _i20.SettingsPage(key: args.key);
    },
  );
}

class SettingsRouteArgs {
  const SettingsRouteArgs({this.key});

  final _i26.Key? key;

  @override
  String toString() {
    return 'SettingsRouteArgs{key: $key}';
  }
}

/// generated route for
/// [_i21.SignUpPage]
class SignUpRoute extends _i25.PageRouteInfo<void> {
  const SignUpRoute({List<_i25.PageRouteInfo>? children})
      : super(SignUpRoute.name, initialChildren: children);

  static const String name = 'SignUpRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i21.SignUpPage();
    },
  );
}

/// generated route for
/// [_i22.SplashPage]
class SplashRoute extends _i25.PageRouteInfo<void> {
  const SplashRoute({List<_i25.PageRouteInfo>? children})
      : super(SplashRoute.name, initialChildren: children);

  static const String name = 'SplashRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i22.SplashPage();
    },
  );
}

/// generated route for
/// [_i23.TroubleshootingPage]
class TroubleshootingRoute extends _i25.PageRouteInfo<void> {
  const TroubleshootingRoute({List<_i25.PageRouteInfo>? children})
      : super(TroubleshootingRoute.name, initialChildren: children);

  static const String name = 'TroubleshootingRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return _i23.TroubleshootingPage();
    },
  );
}

/// generated route for
/// [_i24.WelcomePage]
class WelcomeRoute extends _i25.PageRouteInfo<void> {
  const WelcomeRoute({List<_i25.PageRouteInfo>? children})
      : super(WelcomeRoute.name, initialChildren: children);

  static const String name = 'WelcomeRoute';

  static _i25.PageInfo page = _i25.PageInfo(
    name,
    builder: (data) {
      return const _i24.WelcomePage();
    },
  );
}
